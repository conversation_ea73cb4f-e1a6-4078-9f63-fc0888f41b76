"""
Streamlit主应用
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

import streamlit as st
from config.config import settings
from utils.logger import get_logger

# 页面配置
st.set_page_config(
    page_title=settings.PROJECT_NAME,
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

logger = get_logger(__name__)


def main():
    """主应用函数"""
    # 侧边栏导航
    st.sidebar.title("📈 量化交易系统")
    st.sidebar.markdown("---")
    
    # 页面选择
    pages = {
        "🏠 仪表板": "dashboard",
        "🤖 模型训练": "training", 
        "📊 回测分析": "backtesting",
        "💹 实时交易": "live_trading",
        "⚙️ 系统设置": "settings"
    }
    
    selected_page = st.sidebar.selectbox(
        "选择页面",
        list(pages.keys())
    )
    
    # 系统信息
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 系统信息")
    st.sidebar.info(f"版本: {settings.VERSION}")
    st.sidebar.info(f"调试模式: {'开启' if settings.DEBUG else '关闭'}")
    
    # 根据选择显示页面
    page_name = pages[selected_page]
    
    try:
        if page_name == "dashboard":
            from pages.dashboard import show_dashboard
            show_dashboard()
        elif page_name == "training":
            from pages.training import show_training
            show_training()
        elif page_name == "backtesting":
            from pages.backtesting import show_backtesting
            show_backtesting()
        elif page_name == "live_trading":
            from pages.live_trading import show_live_trading
            show_live_trading()
        elif page_name == "settings":
            show_settings()
    except ImportError as e:
        st.error(f"页面加载失败: {e}")
        st.info("该页面正在开发中...")


def show_settings():
    """显示设置页面"""
    st.title("⚙️ 系统设置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("数据源配置")
        
        # Tushare配置
        tushare_token = st.text_input(
            "Tushare Token",
            value="***" if settings.TUSHARE_TOKEN else "",
            type="password",
            help="请输入您的Tushare Pro Token"
        )
        
        if st.button("测试Tushare连接"):
            if tushare_token and tushare_token != "***":
                try:
                    import tushare as ts
                    ts.set_token(tushare_token)
                    pro = ts.pro_api()
                    # 测试API调用
                    df = pro.stock_basic(list_status='L', limit=1)
                    if not df.empty:
                        st.success("✅ Tushare连接成功!")
                    else:
                        st.error("❌ Tushare连接失败")
                except Exception as e:
                    st.error(f"❌ Tushare连接失败: {e}")
            else:
                st.warning("请输入有效的Tushare Token")
    
    with col2:
        st.subheader("交易配置")
        
        initial_capital = st.number_input(
            "初始资金",
            value=float(settings.INITIAL_CAPITAL),
            min_value=1000.0,
            step=1000.0,
            format="%.2f"
        )
        
        commission_rate = st.number_input(
            "手续费率",
            value=float(settings.COMMISSION_RATE),
            min_value=0.0,
            max_value=0.01,
            step=0.0001,
            format="%.4f"
        )
        
        slippage = st.number_input(
            "滑点",
            value=float(settings.SLIPPAGE),
            min_value=0.0,
            max_value=0.01,
            step=0.0001,
            format="%.4f"
        )
    
    st.markdown("---")
    
    # 强化学习配置
    st.subheader("🤖 强化学习配置")
    
    col3, col4 = st.columns(2)
    
    with col3:
        training_episodes = st.number_input(
            "训练轮数",
            value=settings.TRAINING_EPISODES,
            min_value=100,
            max_value=10000,
            step=100
        )
        
        batch_size = st.number_input(
            "批次大小",
            value=settings.BATCH_SIZE,
            min_value=16,
            max_value=512,
            step=16
        )
    
    with col4:
        learning_rate = st.number_input(
            "学习率",
            value=float(settings.LEARNING_RATE),
            min_value=0.0001,
            max_value=0.1,
            step=0.0001,
            format="%.4f"
        )
    
    # 保存配置按钮
    if st.button("💾 保存配置", type="primary"):
        # 这里可以添加保存配置的逻辑
        st.success("✅ 配置已保存!")
        st.balloons()


if __name__ == "__main__":
    main()
