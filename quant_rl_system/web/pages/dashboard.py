"""
仪表板页面
"""
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import numpy as np

from utils.logger import get_logger

logger = get_logger(__name__)


def show_dashboard():
    """显示仪表板"""
    st.title("🏠 量化交易系统仪表板")
    
    # 顶部指标卡片
    show_metrics_cards()
    
    st.markdown("---")
    
    # 主要图表区域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        show_portfolio_performance()
    
    with col2:
        show_position_allocation()
    
    st.markdown("---")
    
    # 底部区域
    col3, col4 = st.columns(2)
    
    with col3:
        show_recent_trades()
    
    with col4:
        show_market_overview()


def show_metrics_cards():
    """显示指标卡片"""
    col1, col2, col3, col4 = st.columns(4)
    
    # 模拟数据
    total_value = 125680.50
    daily_pnl = 2340.80
    total_return = 0.2568
    win_rate = 0.68
    
    with col1:
        st.metric(
            label="📊 总资产",
            value=f"¥{total_value:,.2f}",
            delta=f"¥{daily_pnl:,.2f}"
        )
    
    with col2:
        st.metric(
            label="📈 总收益率",
            value=f"{total_return:.2%}",
            delta="2.34%"
        )
    
    with col3:
        st.metric(
            label="🎯 胜率",
            value=f"{win_rate:.1%}",
            delta="3.2%"
        )
    
    with col4:
        st.metric(
            label="📉 最大回撤",
            value="-8.5%",
            delta="-1.2%"
        )


def show_portfolio_performance():
    """显示组合表现"""
    st.subheader("📈 组合表现")
    
    # 生成模拟数据
    dates = pd.date_range(start='2024-01-01', end=datetime.now(), freq='D')
    np.random.seed(42)
    
    # 模拟收益率
    returns = np.random.normal(0.001, 0.02, len(dates))
    cumulative_returns = (1 + pd.Series(returns)).cumprod()
    portfolio_value = 100000 * cumulative_returns
    
    # 基准数据（沪深300）
    benchmark_returns = np.random.normal(0.0005, 0.015, len(dates))
    benchmark_cumulative = (1 + pd.Series(benchmark_returns)).cumprod()
    benchmark_value = 100000 * benchmark_cumulative
    
    # 创建图表
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=dates,
        y=portfolio_value,
        mode='lines',
        name='投资组合',
        line=dict(color='#1f77b4', width=2)
    ))
    
    fig.add_trace(go.Scatter(
        x=dates,
        y=benchmark_value,
        mode='lines',
        name='沪深300',
        line=dict(color='#ff7f0e', width=2, dash='dash')
    ))
    
    fig.update_layout(
        title="组合净值走势",
        xaxis_title="日期",
        yaxis_title="净值",
        hovermode='x unified',
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)


def show_position_allocation():
    """显示持仓分配"""
    st.subheader("🥧 持仓分配")
    
    # 模拟持仓数据
    positions = {
        '平安银行': 25.5,
        '招商银行': 20.3,
        '贵州茅台': 18.7,
        '腾讯控股': 15.2,
        '比亚迪': 12.8,
        '其他': 7.5
    }
    
    fig = px.pie(
        values=list(positions.values()),
        names=list(positions.keys()),
        title="持仓分布"
    )
    
    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(height=400)
    
    st.plotly_chart(fig, use_container_width=True)


def show_recent_trades():
    """显示最近交易"""
    st.subheader("📋 最近交易")
    
    # 模拟交易数据
    trades_data = {
        '时间': ['2024-01-15 14:30', '2024-01-15 11:20', '2024-01-14 15:45', '2024-01-14 10:15'],
        '股票': ['平安银行', '招商银行', '贵州茅台', '腾讯控股'],
        '操作': ['买入', '卖出', '买入', '买入'],
        '数量': [1000, 500, 100, 200],
        '价格': [12.45, 45.67, 1680.50, 345.20],
        '盈亏': ['+234.50', '-123.80', '+567.30', '+89.20']
    }
    
    df = pd.DataFrame(trades_data)
    
    # 添加颜色标识
    def color_pnl(val):
        if val.startswith('+'):
            return 'color: green'
        elif val.startswith('-'):
            return 'color: red'
        return ''
    
    styled_df = df.style.applymap(color_pnl, subset=['盈亏'])
    st.dataframe(styled_df, use_container_width=True)


def show_market_overview():
    """显示市场概览"""
    st.subheader("🌍 市场概览")
    
    # 模拟市场数据
    market_data = {
        '指数': ['上证指数', '深证成指', '创业板指', '科创50'],
        '最新价': [3245.67, 10234.56, 2156.78, 1023.45],
        '涨跌幅': ['+1.23%', '-0.56%', '+2.34%', '+0.89%'],
        '成交量': ['234.5亿', '345.6亿', '123.4亿', '67.8亿']
    }
    
    df = pd.DataFrame(market_data)
    
    # 添加颜色标识
    def color_change(val):
        if val.startswith('+'):
            return 'color: red'  # 中国股市红涨绿跌
        elif val.startswith('-'):
            return 'color: green'
        return ''
    
    styled_df = df.style.applymap(color_change, subset=['涨跌幅'])
    st.dataframe(styled_df, use_container_width=True)
    
    # 市场情绪指标
    st.markdown("### 📊 市场情绪")
    
    sentiment_col1, sentiment_col2 = st.columns(2)
    
    with sentiment_col1:
        st.metric("恐慌贪婪指数", "65", "5")
    
    with sentiment_col2:
        st.metric("资金流向", "净流入", "12.3亿")


if __name__ == "__main__":
    show_dashboard()
