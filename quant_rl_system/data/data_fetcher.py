"""
数据获取模块
"""
import pandas as pd
import tushare as ts
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from pathlib import Path

from config.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class DataFetcher:
    """数据获取器"""
    
    def __init__(self):
        """初始化数据获取器"""
        # 设置Tushare token
        ts.set_token(settings.TUSHARE_TOKEN)
        self.pro = ts.pro_api()
        
        # 创建数据目录
        self.data_path = Path(settings.DATA_PATH)
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("数据获取器初始化完成")
    
    def get_stock_basic(self) -> pd.DataFrame:
        """获取股票基本信息"""
        try:
            logger.info("获取股票基本信息...")
            df = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            # 保存到本地
            file_path = self.data_path / "stock_basic.csv"
            df.to_csv(file_path, index=False)
            logger.info(f"股票基本信息已保存到: {file_path}")
            
            return df
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return pd.DataFrame()
    
    def get_daily_data(
        self,
        ts_code: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """获取日线数据"""
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            logger.info(f"获取 {ts_code} 日线数据: {start_date} - {end_date}")
            
            df = self.pro.daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
            )
            
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的数据")
                return df
            
            # 数据预处理
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date').reset_index(drop=True)
            
            # 保存到本地
            file_path = self.data_path / f"{ts_code}_daily.csv"
            df.to_csv(file_path, index=False)
            logger.info(f"日线数据已保存到: {file_path}")
            
            return df
        except Exception as e:
            logger.error(f"获取 {ts_code} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        ts_code: str,
        freq: str = '5min',
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """获取分钟级数据"""
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            logger.info(f"获取 {ts_code} {freq} 数据: {start_date} - {end_date}")
            
            df = ts.pro_bar(
                ts_code=ts_code,
                freq=freq,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的分钟数据")
                return df
            
            # 数据预处理
            df['trade_time'] = pd.to_datetime(df['trade_time'])
            df = df.sort_values('trade_time').reset_index(drop=True)
            
            # 保存到本地
            file_path = self.data_path / f"{ts_code}_{freq}.csv"
            df.to_csv(file_path, index=False)
            logger.info(f"分钟数据已保存到: {file_path}")
            
            return df
        except Exception as e:
            logger.error(f"获取 {ts_code} 分钟数据失败: {e}")
            return pd.DataFrame()
    
    def get_index_data(
        self,
        ts_code: str = '000001.SH',  # 上证指数
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """获取指数数据"""
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            logger.info(f"获取指数 {ts_code} 数据: {start_date} - {end_date}")
            
            df = self.pro.index_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning(f"未获取到指数 {ts_code} 的数据")
                return df
            
            # 数据预处理
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date').reset_index(drop=True)
            
            # 保存到本地
            file_path = self.data_path / f"index_{ts_code}_daily.csv"
            df.to_csv(file_path, index=False)
            logger.info(f"指数数据已保存到: {file_path}")
            
            return df
        except Exception as e:
            logger.error(f"获取指数 {ts_code} 数据失败: {e}")
            return pd.DataFrame()
    
    def update_all_data(self, symbols: Optional[List[str]] = None):
        """更新所有数据"""
        logger.info("开始更新所有数据...")
        
        # 更新股票基本信息
        self.get_stock_basic()
        
        # 更新指数数据
        self.get_index_data('000001.SH')  # 上证指数
        self.get_index_data('399001.SZ')  # 深证成指
        self.get_index_data('399006.SZ')  # 创业板指
        
        # 更新指定股票数据
        if symbols:
            for symbol in symbols:
                self.get_daily_data(symbol)
        
        logger.info("所有数据更新完成")


if __name__ == "__main__":
    # 测试数据获取
    fetcher = DataFetcher()
    
    # 获取平安银行数据
    data = fetcher.get_daily_data('000001.SZ')
    print(f"获取到 {len(data)} 条数据")
    print(data.head())
