"""
强化学习交易环境
"""
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Tuple, Optional
from datetime import datetime

from config.config import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class TradingEnvironment(gym.Env):
    """交易环境类"""
    
    metadata = {'render.modes': ['human']}
    
    def __init__(
        self,
        data: pd.DataFrame,
        initial_capital: float = None,
        commission_rate: float = None,
        slippage: float = None,
        lookback_window: int = 20
    ):
        """
        初始化交易环境
        
        Args:
            data: 股票数据
            initial_capital: 初始资金
            commission_rate: 手续费率
            slippage: 滑点
            lookback_window: 回望窗口
        """
        super().__init__()
        
        self.data = data.copy()
        self.initial_capital = initial_capital or settings.INITIAL_CAPITAL
        self.commission_rate = commission_rate or settings.COMMISSION_RATE
        self.slippage = slippage or settings.SLIPPAGE
        self.lookback_window = lookback_window
        
        # 环境状态
        self.current_step = 0
        self.max_steps = len(data) - lookback_window - 1
        
        # 账户状态
        self.cash = self.initial_capital
        self.position = 0  # 持仓数量
        self.total_value = self.initial_capital
        self.trades = []
        
        # 定义动作空间：0=持有, 1=买入, 2=卖出
        self.action_space = spaces.Discrete(3)
        
        # 定义观察空间：价格特征 + 技术指标 + 账户状态
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self._get_observation_dim(),),
            dtype=np.float32
        )
        
        # 预处理数据
        self._preprocess_data()
        
        logger.info(f"交易环境初始化完成，数据长度: {len(self.data)}")
    
    def _preprocess_data(self):
        """预处理数据，计算技术指标"""
        # 确保数据按时间排序
        if 'trade_date' in self.data.columns:
            self.data = self.data.sort_values('trade_date').reset_index(drop=True)
        
        # 计算收益率
        self.data['returns'] = self.data['close'].pct_change()
        
        # 计算移动平均线
        self.data['ma5'] = self.data['close'].rolling(5).mean()
        self.data['ma10'] = self.data['close'].rolling(10).mean()
        self.data['ma20'] = self.data['close'].rolling(20).mean()
        
        # 计算RSI
        self.data['rsi'] = self._calculate_rsi(self.data['close'])
        
        # 计算MACD
        macd_data = self._calculate_macd(self.data['close'])
        self.data['macd'] = macd_data['macd']
        self.data['macd_signal'] = macd_data['signal']
        self.data['macd_hist'] = macd_data['histogram']
        
        # 计算布林带
        bollinger = self._calculate_bollinger_bands(self.data['close'])
        self.data['bb_upper'] = bollinger['upper']
        self.data['bb_middle'] = bollinger['middle']
        self.data['bb_lower'] = bollinger['lower']
        
        # 填充NaN值
        self.data = self.data.fillna(method='bfill').fillna(0)
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram
        }
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict:
        """计算布林带"""
        middle = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }
    
    def _get_observation_dim(self) -> int:
        """获取观察空间维度"""
        # 价格特征(5) + 技术指标(8) + 账户状态(3) = 16
        return 16
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观察"""
        if self.current_step < self.lookback_window:
            # 如果步数不够，用0填充
            return np.zeros(self._get_observation_dim(), dtype=np.float32)
        
        # 获取当前数据
        current_idx = self.lookback_window + self.current_step
        current_data = self.data.iloc[current_idx]
        
        # 价格特征（归一化）
        price_features = np.array([
            current_data['open'] / current_data['close'],
            current_data['high'] / current_data['close'],
            current_data['low'] / current_data['close'],
            current_data['volume'] / 1e6,  # 成交量（百万）
            current_data['returns']
        ])
        
        # 技术指标（归一化）
        tech_features = np.array([
            current_data['ma5'] / current_data['close'],
            current_data['ma10'] / current_data['close'],
            current_data['ma20'] / current_data['close'],
            current_data['rsi'] / 100,
            current_data['macd'] / current_data['close'],
            current_data['macd_signal'] / current_data['close'],
            current_data['bb_upper'] / current_data['close'],
            current_data['bb_lower'] / current_data['close']
        ])
        
        # 账户状态（归一化）
        account_features = np.array([
            self.cash / self.initial_capital,
            self.position / 1000,  # 假设最大持仓1000股
            self.total_value / self.initial_capital
        ])
        
        observation = np.concatenate([price_features, tech_features, account_features])
        
        # 处理NaN和无穷值
        observation = np.nan_to_num(observation, nan=0.0, posinf=1.0, neginf=-1.0)
        
        return observation.astype(np.float32)
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """执行动作"""
        if self.current_step >= self.max_steps:
            return self._get_observation(), 0, True, False, {}
        
        # 获取当前价格
        current_idx = self.lookback_window + self.current_step
        current_price = self.data.iloc[current_idx]['close']
        
        # 执行交易动作
        reward = self._execute_trade(action, current_price)
        
        # 更新步数
        self.current_step += 1
        
        # 检查是否结束
        done = self.current_step >= self.max_steps
        
        # 更新总价值
        if self.position > 0:
            self.total_value = self.cash + self.position * current_price
        else:
            self.total_value = self.cash
        
        # 获取新观察
        observation = self._get_observation()
        
        # 信息字典
        info = {
            'cash': self.cash,
            'position': self.position,
            'total_value': self.total_value,
            'current_price': current_price
        }
        
        return observation, reward, done, False, info
    
    def _execute_trade(self, action: int, price: float) -> float:
        """执行交易并计算奖励"""
        reward = 0
        
        # 考虑滑点
        actual_price = price * (1 + self.slippage if action == 1 else 1 - self.slippage)
        
        if action == 1:  # 买入
            if self.cash > actual_price * 100:  # 至少买100股
                shares_to_buy = int(self.cash / actual_price / 100) * 100
                cost = shares_to_buy * actual_price * (1 + self.commission_rate)
                
                if cost <= self.cash:
                    self.cash -= cost
                    self.position += shares_to_buy
                    
                    self.trades.append({
                        'action': 'buy',
                        'price': actual_price,
                        'shares': shares_to_buy,
                        'timestamp': self.current_step
                    })
                    
                    reward = -self.commission_rate  # 交易成本惩罚
        
        elif action == 2:  # 卖出
            if self.position > 0:
                proceeds = self.position * actual_price * (1 - self.commission_rate)
                self.cash += proceeds
                
                self.trades.append({
                    'action': 'sell',
                    'price': actual_price,
                    'shares': self.position,
                    'timestamp': self.current_step
                })
                
                self.position = 0
                reward = -self.commission_rate  # 交易成本惩罚
        
        # 持仓奖励：基于价格变化
        if len(self.data) > self.lookback_window + self.current_step + 1:
            next_price = self.data.iloc[self.lookback_window + self.current_step + 1]['close']
            price_change = (next_price - price) / price
            
            if self.position > 0:
                reward += price_change  # 持仓时获得价格变化奖励
        
        return reward
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """重置环境"""
        super().reset(seed=seed)
        
        self.current_step = 0
        self.cash = self.initial_capital
        self.position = 0
        self.total_value = self.initial_capital
        self.trades = []
        
        observation = self._get_observation()
        info = {
            'cash': self.cash,
            'position': self.position,
            'total_value': self.total_value
        }
        
        return observation, info
    
    def render(self, mode: str = 'human'):
        """渲染环境"""
        if mode == 'human':
            current_idx = self.lookback_window + self.current_step
            if current_idx < len(self.data):
                current_price = self.data.iloc[current_idx]['close']
                print(f"Step: {self.current_step}, Price: {current_price:.2f}, "
                      f"Cash: {self.cash:.2f}, Position: {self.position}, "
                      f"Total Value: {self.total_value:.2f}")


if __name__ == "__main__":
    # 测试环境
    from data.data_fetcher import DataFetcher
    
    # 获取测试数据
    fetcher = DataFetcher()
    data = fetcher.get_daily_data('000001.SZ')
    
    if not data.empty:
        # 创建环境
        env = TradingEnvironment(data)
        
        # 测试环境
        obs, info = env.reset()
        print(f"初始观察维度: {obs.shape}")
        print(f"初始信息: {info}")
        
        # 随机测试几步
        for i in range(5):
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            print(f"Step {i}: Action={action}, Reward={reward:.4f}, Done={done}")
            
            if done:
                break
    else:
        print("无法获取测试数据")
