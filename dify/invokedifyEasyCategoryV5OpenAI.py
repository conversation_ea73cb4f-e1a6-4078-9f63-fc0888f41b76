import math

import pandas as pd
import requests
import json
import time
import ast


# ===== 配置参数 =====
INPUT_FILE = "/Users/<USER>/Downloads/miss_category/chunk_11.xlsx"
OUTPUT_FILE = "/Users/<USER>/Downloads/miss_category/output/chunk_11_output.xlsx"
DIFY_API_URL = "http://192.168.56.48/v1/workflows/run"
API_KEY = "app-j7FldY81poBj3Se807UchNw7"
MODEL_COUNT = 1  # 需要调用的模型数量

# ===== 1. 读取Excel文件 =====
df = pd.read_excel(INPUT_FILE, engine='openpyxl')

# ===== 2. 准备新列结构 =====
new_columns = [
  "AI_matchedDepartment",
  "AI_matchedCategory",
  "AI_matchedSubCategory",
  "AI_matchedClazz",
  "AI_Matched_Credibility",
  "AI_Recommended_Category",
  "AI_Recommended_Credibility",
  "Total_Tokens", "Total_Steps", "Elapsed_Time",  # 添加新字段
  "AI_analysis",
  "AI_category_optimization_suggestions",  # 添加缺失的列
  "Workflow_ID"
]

# 添加各模型的列
for model_num in range(1, MODEL_COUNT+1):
  model_columns = [
    f"model_{model_num}_AI_Matched_Category",
    f"model_{model_num}_AI_Recommended_Category",
    f"model_{model_num}_AI_Overall_Credibility"
  ]
  new_columns.extend(model_columns)

# 初始化新列，明确设置为object类型以避免dtype警告
for col in new_columns:
  df[col] = None
  df[col] = df[col].astype('object')

def extract_json_from_output(text: str):
  try:
    # 优先使用安全的 Python 字面量解析器
    parsed = ast.literal_eval(text)
    if isinstance(parsed, dict) and "final_result" in parsed:
      print(f"成功从 output_str 直接提取字典: {parsed}")
      return parsed
  except (ValueError, SyntaxError):
    pass  # 继续使用原始方法

  # fallback 逻辑
  start = 0
  while True:
    start = text.find('{', start)
    if start == -1:
      break
    for end in range(len(text), start, -1):
      try:
        candidate = text[start:end]
        parsed = json.loads(candidate)
        if "final_result" in parsed:
          print(f"提取JSON数据, parsed: {parsed}")
          return parsed
      except json.JSONDecodeError:
        continue
    start += 1
  return None

def sanitize_inputs(data):
  clean_data = {}
  for k, v in data.items():
    if isinstance(v, float) and (math.isnan(v) or math.isinf(v)):
      clean_data[k] = None
    else:
      clean_data[k] = v
  return clean_data

def call_dify_workflow(row_data):
  """调用Dify API并处理流式响应"""
  payload = {
    "inputs": sanitize_inputs({
      "description": row_data['Title'],
    }),
    "response_mode": "streaming",
    "user": "leo"
  }

  headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
  }

  try:
    print("开始请求Dify")
    response = requests.post(DIFY_API_URL, json=payload, headers=headers, stream=True)
    response.raise_for_status()
    
    workflow_run_id = None
    total_tokens = None
    total_steps = None
    elapsed_time = None
    
    # 处理流式响应
    for line in response.iter_lines():
      if not line:
        continue
        
      # 解析SSE格式的数据
      line_text = line.decode('utf-8')
      if line_text.startswith('data:'):
        data_str = line_text[5:].strip()
        try:
          data = json.loads(data_str)
          
          # 检查是否是workflow_started事件，获取workflow_run_id
          if data.get('event') == 'workflow_started':
            workflow_run_id = data.get('workflow_run_id')
            print(f"Workflow started with ID: {workflow_run_id}")
            
          # 检查是否是workflow_finished事件
          if data.get('event') == 'workflow_finished':
            if data.get('data', {}).get('status') == 'succeeded':
              # 获取性能指标
              total_tokens = data.get('data', {}).get('total_tokens')
              total_steps = data.get('data', {}).get('total_steps')
              elapsed_time = data.get('data', {}).get('elapsed_time')
              
              # 提取output中的JSON数据
              output_str = data.get('data', {}).get('outputs', {}).get('output', '{}')
              
              # 使用新函数提取JSON
              result_json = output_str

              if result_json:
                # 添加性能指标到结果中
                result_json['total_tokens'] = total_tokens
                result_json['total_steps'] = total_steps
                result_json['elapsed_time'] = elapsed_time
                result_json['workflow_run_id'] = workflow_run_id
                print("成功获取结果")
                return result_json
              else:
                print("无法从输出中提取JSON数据")
                return {
                  'total_tokens': total_tokens,
                  'total_steps': total_steps,
                  'elapsed_time': elapsed_time,
                  'workflow_run_id': workflow_run_id
                }
        except json.JSONDecodeError as e:
          print(f"JSON解析错误: {e}")
          
    # 如果没有在流式响应中获取到结果，但有workflow_run_id，可以尝试获取结果
    if workflow_run_id:
      print(f"通过workflow_run_id获取结果: {workflow_run_id}")
      return get_workflow_result(workflow_run_id)
      
    return {}
    
  except requests.exceptions.RequestException as e:
    print(f"API调用失败: {e}")
    return {}

def get_workflow_result(workflow_run_id):
  """根据workflow_run_id获取执行结果"""
  result_url = f"{DIFY_API_URL}/{workflow_run_id}"
  headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
  }
  
  # 尝试获取结果，最多重试20次
  for _ in range(20):
    try:
      response = requests.get(result_url, headers=headers)
      response.raise_for_status()
      data = response.json()
      
      if data.get('status') == 'succeeded':
        # 获取性能指标
        total_tokens = data.get('total_tokens')
        total_steps = data.get('total_steps')
        elapsed_time = data.get('elapsed_time')
        
        output_str = data.get('outputs', {}).get('output', '{}')
        
        # 使用新函数提取JSON
        result_json = extract_json_from_output(output_str)
        if result_json:
          # 添加性能指标到结果中
          result_json['total_tokens'] = total_tokens
          result_json['total_steps'] = total_steps
          result_json['elapsed_time'] = elapsed_time
          result_json['workflow_run_id'] = workflow_run_id
          return result_json
        else:
          print("无法从输出中提取JSON数据")
          return {
            'total_tokens': total_tokens,
            'total_steps': total_steps,
            'elapsed_time': elapsed_time,
            'workflow_run_id': workflow_run_id
          }
      
      elif data.get('status') == 'failed':
        print(f"Workflow执行失败: {data.get('error')}")
        return {}
        
      # 如果还在执行中，等待后重试
      time.sleep(20)
      
    except requests.exceptions.RequestException as e:
      print(f"获取结果失败: {e}")
      time.sleep(20)
  
  print(f"获取workflow结果超时: {workflow_run_id}")
  return {}

# ===== 4. 处理每行数据 =====
for index, row in df.iterrows():
  print(f"处理第 {index+1} 行数据")
  
  # 调用Dify获取结果
  result = call_dify_workflow(row)

  if result:
    # 提取性能指标
    df.at[index, 'Total_Tokens'] = result.get('total_tokens')
    df.at[index, 'Total_Steps'] = result.get('total_steps')
    df.at[index, 'Elapsed_Time'] = result.get('elapsed_time')
    df.at[index, 'Workflow_ID'] = result.get('workflow_run_id')
    
    # 提取final_result数据
    final_result = result.get('finalResult', {})
    if final_result:
      df.at[index, 'AI_matchedDepartment'] = final_result.get('matchedDepartment')
      df.at[index, 'AI_matchedCategory'] = final_result.get('matchedCategory')
      df.at[index, 'AI_matchedSubCategory'] = final_result.get('matchedSubCategory')
      df.at[index, 'AI_matchedClazz'] = final_result.get('matchedClazz')
      df.at[index, 'AI_Matched_Credibility'] = final_result.get('matchedCredibility')
      df.at[index, 'AI_Recommended_Category'] = final_result.get('recommendedCategory')
      df.at[index, 'AI_Recommended_Credibility'] = final_result.get('recommendedCredibility')
    
    # 多模型处理
    for model_num in range(1, MODEL_COUNT+1):
      model_response_prefix = f"model_{model_num}_result"
      model_response = result.get(model_response_prefix, {})
      if model_response:
        prefix = f"model_{model_num}_"
        df.at[index, f"{prefix}AI_Matched_Category"] = model_response.get('Matched_Category')
        df.at[index, f"{prefix}AI_Recommended_Category"] = model_response.get('Recommended_Category')
        df.at[index, f"{prefix}AI_Overall_Credibility"] = model_response.get('Total_Credibility')

    df.at[index, 'AI_analysis'] = result.get('analysis')
    df.at[index, 'AI_category_optimization_suggestions'] = result.get('categoryOptimizationSuggestions')


# ===== 5. 保存结果 =====
df.to_excel(OUTPUT_FILE, index=False, engine='openpyxl')